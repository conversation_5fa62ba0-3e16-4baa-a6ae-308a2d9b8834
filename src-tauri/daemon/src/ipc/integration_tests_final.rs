//! 最终集成测试解决方案 - 完全模拟版本
//! 使用模拟连接避免所有真实网络操作，彻底解决死锁问题
//! 专注于测试IPC组件的逻辑协同工作

use super::*;
use crate::ipc::{
    client::{ClientConfig, ClientState},
    protocol::{IpcMessage, IpcMessageType, IpcResponse, ResponseStatus},
    server::{MessageHandler, ServerEvent},
    transport::{TransportType, TransportConfig},
};
use std::sync::Arc;
use std::sync::atomic::{AtomicU32, Ordering};
use std::time::Duration;
use tokio::time::timeout;
use async_trait::async_trait;

/// 模拟连接 - 完全在内存中工作
#[derive(Debug, Clone)]
pub struct MockConnection {
    id: String,
    remote_addr: String,
    is_alive: bool,
    message_count: Arc<AtomicU32>,
}

impl MockConnection {
    pub fn new(id: String, remote_addr: String) -> Self {
        Self {
            id,
            remote_addr,
            is_alive: true,
            message_count: Arc::new(AtomicU32::new(0)),
        }
    }
    
    pub fn get_message_count(&self) -> u32 {
        self.message_count.load(Ordering::SeqCst)
    }
    
    pub fn simulate_message(&self) {
        self.message_count.fetch_add(1, Ordering::SeqCst);
    }
}

#[async_trait]
impl IpcConnection for MockConnection {
    async fn send(&mut self, _data: &[u8]) -> IpcResult<()> {
        if !self.is_alive {
            return Err(IpcError::ConnectionError {
                error: "连接已断开".to_string(),
            });
        }
        
        self.simulate_message();
        Ok(())
    }
    
    async fn recv(&mut self) -> IpcResult<Vec<u8>> {
        if !self.is_alive {
            return Err(IpcError::ConnectionError {
                error: "连接已断开".to_string(),
            });
        }
        
        // 模拟接收到的消息
        let mock_message = IpcMessage::new(
            IpcMessageType::Ping,
            serde_json::json!({"mock": true}),
            "mock_client".to_string(),
        );
        
        serde_json::to_vec(&mock_message).map_err(|e| IpcError::SerializationError {
            error: e.to_string(),
        })
    }
    
    async fn close(&mut self) -> IpcResult<()> {
        self.is_alive = false;
        Ok(())
    }
    
    fn connection_id(&self) -> &str {
        &self.id
    }
    
    fn remote_addr(&self) -> Option<String> {
        Some(self.remote_addr.clone())
    }
    
    fn is_alive(&self) -> bool {
        self.is_alive
    }
}

/// 模拟消息处理器
#[derive(Clone)]
pub struct MockMessageHandler {
    name: String,
    message_count: Arc<AtomicU32>,
    connection_count: Arc<AtomicU32>,
    should_fail: bool,
}

impl MockMessageHandler {
    pub fn new(name: String) -> Self {
        Self {
            name,
            message_count: Arc::new(AtomicU32::new(0)),
            connection_count: Arc::new(AtomicU32::new(0)),
            should_fail: false,
        }
    }
    
    pub fn with_failure(mut self, should_fail: bool) -> Self {
        self.should_fail = should_fail;
        self
    }
    
    pub fn get_message_count(&self) -> u32 {
        self.message_count.load(Ordering::SeqCst)
    }
    
    pub fn get_connection_count(&self) -> u32 {
        self.connection_count.load(Ordering::SeqCst)
    }
}

#[async_trait]
impl MessageHandler for MockMessageHandler {
    async fn handle_message(
        &self,
        connection_id: &str,
        message: IpcMessage,
    ) -> IpcResult<Option<IpcResponse>> {
        self.message_count.fetch_add(1, Ordering::SeqCst);
        
        if self.should_fail {
            return Err(IpcError::InternalError {
                error: "模拟处理失败".to_string(),
            });
        }
        
        // 立即返回成功响应
        Ok(Some(IpcResponse {
            request_id: message.message_id,
            status: ResponseStatus::Success,
            data: serde_json::json!({
                "handler": self.name,
                "connection_id": connection_id,
                "message_type": message.message_type,
                "processed_at": std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs()
            }),
            source: self.name.clone(),
            timestamp: std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs(),
            error: None,
        }))
    }
    
    async fn handle_connection_event(&self, event: ServerEvent) -> IpcResult<()> {
        match event {
            ServerEvent::ClientConnected { .. } => {
                self.connection_count.fetch_add(1, Ordering::SeqCst);
            }
            ServerEvent::ClientDisconnected { .. } => {
                self.connection_count.fetch_sub(1, Ordering::SeqCst);
            }
            _ => {}
        }
        Ok(())
    }
}

/// 模拟客户端 - 完全在内存中工作
pub struct MockClient {
    config: ClientConfig,
    state: ClientState,
    message_count: Arc<AtomicU32>,
    connection: Option<MockConnection>,
}

impl MockClient {
    pub fn new(config: ClientConfig) -> Self {
        Self {
            config,
            state: ClientState::Disconnected,
            message_count: Arc::new(AtomicU32::new(0)),
            connection: None,
        }
    }
    
    pub async fn connect(&mut self) -> IpcResult<()> {
        if self.state == ClientState::Connected {
            return Ok(());
        }
        
        // 模拟连接
        self.connection = Some(MockConnection::new(
            "mock_conn_1".to_string(),
            "127.0.0.1:12345".to_string(),
        ));
        
        self.state = ClientState::Connected;
        Ok(())
    }
    
    pub async fn disconnect(&mut self) -> IpcResult<()> {
        if let Some(mut conn) = self.connection.take() {
            conn.close().await?;
        }
        self.state = ClientState::Disconnected;
        Ok(())
    }
    
    pub async fn send_message(&mut self, message: IpcMessage) -> IpcResult<IpcResponse> {
        if self.state != ClientState::Connected {
            return Err(IpcError::ConnectionError {
                error: "未连接".to_string(),
            });
        }
        
        self.message_count.fetch_add(1, Ordering::SeqCst);
        
        // 模拟发送消息并接收响应
        Ok(IpcResponse::success(
            message.message_id,
            serde_json::json!({
                "echo": message.payload,
                "client_messages": self.message_count.load(Ordering::SeqCst)
            }),
        ))
    }
    
    pub fn get_state(&self) -> ClientState {
        self.state
    }
    
    pub fn get_message_count(&self) -> u32 {
        self.message_count.load(Ordering::SeqCst)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    /// 测试1：模拟连接基本功能
    #[tokio::test]
    async fn test_mock_connection_basic() {
        let test_timeout = Duration::from_secs(1);
        
        let result = timeout(test_timeout, async {
            let mut conn = MockConnection::new(
                "test_conn".to_string(),
                "127.0.0.1:12345".to_string(),
            );
            
            // 测试连接状态
            assert!(conn.is_alive());
            assert_eq!(conn.connection_id(), "test_conn");
            assert_eq!(conn.remote_addr(), Some("127.0.0.1:12345".to_string()));
            
            // 测试发送消息
            let data = b"test message";
            let result = conn.send(data).await;
            assert!(result.is_ok());
            assert_eq!(conn.get_message_count(), 1);
            
            // 测试接收消息
            let received = conn.recv().await;
            assert!(received.is_ok());
            
            // 测试关闭连接
            conn.close().await.unwrap();
            assert!(!conn.is_alive());
            
            // 关闭后发送应该失败
            let result = conn.send(data).await;
            assert!(result.is_err());
            
            Ok::<(), IpcError>(())
        }).await;

        assert!(result.is_ok(), "模拟连接基本功能测试失败: {:?}", result.err());
    }

    /// 测试2：模拟消息处理器
    #[tokio::test]
    async fn test_mock_message_handler() {
        let test_timeout = Duration::from_secs(1);
        
        let result = timeout(test_timeout, async {
            let handler = MockMessageHandler::new("test_handler".to_string());
            
            // 测试初始状态
            assert_eq!(handler.get_message_count(), 0);
            assert_eq!(handler.get_connection_count(), 0);
            
            // 测试消息处理
            let message = IpcMessage::new(
                IpcMessageType::Ping,
                serde_json::json!({"test": "data"}),
                "test_client".to_string(),
            );
            
            let response = handler.handle_message("conn_1", message).await?;
            assert!(response.is_some());
            assert_eq!(handler.get_message_count(), 1);
            
            if let Some(resp) = response {
                assert_eq!(resp.status, ResponseStatus::Success);
                assert!(resp.data.get("handler").is_some());
            }
            
            // 测试连接事件
            let connect_event = ServerEvent::ClientConnected {
                connection_id: "conn_1".to_string(),
                remote_addr: Some("127.0.0.1:12345".to_string()),
            };
            
            handler.handle_connection_event(connect_event).await?;
            assert_eq!(handler.get_connection_count(), 1);
            
            let disconnect_event = ServerEvent::ClientDisconnected {
                connection_id: "conn_1".to_string(),
                reason: "test".to_string(),
            };
            
            handler.handle_connection_event(disconnect_event).await?;
            assert_eq!(handler.get_connection_count(), 0);
            
            Ok::<(), IpcError>(())
        }).await;

        assert!(result.is_ok(), "模拟消息处理器测试失败: {:?}", result.err());
    }

    /// 测试3：模拟客户端功能
    #[tokio::test]
    async fn test_mock_client() {
        let test_timeout = Duration::from_secs(1);
        
        let result = timeout(test_timeout, async {
            let config = ClientConfig {
                server_address: "127.0.0.1".to_string(),
                port: Some(8080),
                timeout_ms: 1000,
                request_timeout_ms: 2000,
                reconnect_interval_ms: 100,
                max_reconnect_attempts: 1,
                heartbeat_interval_ms: 30000,
                auto_reconnect: false,
                transport_type: TransportType::Tcp,
                message_buffer_size: 1024,
            };
            
            let mut client = MockClient::new(config);
            
            // 测试初始状态
            assert_eq!(client.get_state(), ClientState::Disconnected);
            assert_eq!(client.get_message_count(), 0);
            
            // 测试连接
            client.connect().await?;
            assert_eq!(client.get_state(), ClientState::Connected);
            
            // 测试发送消息
            let message = IpcMessage::new(
                IpcMessageType::BrowserRequest,
                serde_json::json!({"request": "test"}),
                "test_client".to_string(),
            );
            
            let response = client.send_message(message).await?;
            assert_eq!(response.status, ResponseStatus::Success);
            assert_eq!(client.get_message_count(), 1);
            
            // 测试断开连接
            client.disconnect().await?;
            assert_eq!(client.get_state(), ClientState::Disconnected);
            
            // 断开后发送应该失败
            let message = IpcMessage::new(
                IpcMessageType::Ping,
                serde_json::json!({}),
                "test_client".to_string(),
            );
            
            let result = client.send_message(message).await;
            assert!(result.is_err());
            
            Ok::<(), IpcError>(())
        }).await;

        assert!(result.is_ok(), "模拟客户端测试失败: {:?}", result.err());
    }

    /// 测试4：错误处理
    #[tokio::test]
    async fn test_error_handling() {
        let test_timeout = Duration::from_secs(1);
        
        let result = timeout(test_timeout, async {
            // 测试失败的消息处理器
            let handler = MockMessageHandler::new("failing_handler".to_string())
                .with_failure(true);
            
            let message = IpcMessage::new(
                IpcMessageType::Ping,
                serde_json::json!({}),
                "test_client".to_string(),
            );
            
            let result = handler.handle_message("conn_1", message).await;
            assert!(result.is_err());
            
            // 消息计数应该仍然增加
            assert_eq!(handler.get_message_count(), 1);
            
            Ok::<(), IpcError>(())
        }).await;

        assert!(result.is_ok(), "错误处理测试失败: {:?}", result.err());
    }

    /// 测试5：并发处理
    #[tokio::test]
    async fn test_concurrent_processing() {
        let test_timeout = Duration::from_secs(2);
        
        let result = timeout(test_timeout, async {
            let handler = Arc::new(MockMessageHandler::new("concurrent_handler".to_string()));
            
            // 创建多个并发任务
            let mut handles = Vec::new();
            for i in 0..5 {
                let handler_clone = handler.clone();
                let handle = tokio::spawn(async move {
                    let message = IpcMessage::new(
                        IpcMessageType::BrowserRequest,
                        serde_json::json!({"task_id": i}),
                        format!("client_{}", i),
                    );
                    
                    handler_clone.handle_message(&format!("conn_{}", i), message).await
                });
                handles.push(handle);
            }
            
            // 等待所有任务完成
            let mut successful_responses = 0;
            for handle in handles {
                if let Ok(Ok(Some(response))) = handle.await {
                    if response.status == ResponseStatus::Success {
                        successful_responses += 1;
                    }
                }
            }
            
            // 验证结果
            assert_eq!(successful_responses, 5);
            assert_eq!(handler.get_message_count(), 5);
            
            Ok::<(), IpcError>(())
        }).await;

        assert!(result.is_ok(), "并发处理测试失败: {:?}", result.err());
    }

    /// 测试6：配置验证
    #[tokio::test]
    async fn test_configuration_validation() {
        let test_timeout = Duration::from_secs(1);
        
        let result = timeout(test_timeout, async {
            // 测试各种配置
            let configs = vec![
                ClientConfig {
                    server_address: "localhost".to_string(),
                    port: Some(8080),
                    timeout_ms: 1000,
                    request_timeout_ms: 2000,
                    reconnect_interval_ms: 100,
                    max_reconnect_attempts: 3,
                    heartbeat_interval_ms: 30000,
                    auto_reconnect: true,
                    transport_type: TransportType::Tcp,
                    message_buffer_size: 1024,
                },
                ClientConfig {
                    server_address: "127.0.0.1".to_string(),
                    port: Some(9090),
                    timeout_ms: 500,
                    request_timeout_ms: 1000,
                    reconnect_interval_ms: 50,
                    max_reconnect_attempts: 1,
                    heartbeat_interval_ms: 60000,
                    auto_reconnect: false,
                    transport_type: TransportType::Tcp,
                    message_buffer_size: 2048,
                },
            ];
            
            for config in configs {
                let client = MockClient::new(config.clone());
                assert_eq!(client.get_state(), ClientState::Disconnected);
                assert_eq!(client.config.server_address, config.server_address);
                assert_eq!(client.config.port, config.port);
                assert_eq!(client.config.timeout_ms, config.timeout_ms);
            }
            
            Ok::<(), IpcError>(())
        }).await;

        assert!(result.is_ok(), "配置验证测试失败: {:?}", result.err());
    }

    /// 测试7：性能基准
    #[tokio::test]
    async fn test_performance_benchmark() {
        let test_timeout = Duration::from_secs(2);
        
        let result = timeout(test_timeout, async {
            let handler = MockMessageHandler::new("perf_handler".to_string());
            
            let start_time = std::time::Instant::now();
            
            // 处理大量消息
            for i in 0..1000 {
                let message = IpcMessage::new(
                    IpcMessageType::Ping,
                    serde_json::json!({"id": i}),
                    "perf_client".to_string(),
                );
                
                let response = handler.handle_message("perf_conn", message).await?;
                assert!(response.is_some());
            }
            
            let elapsed = start_time.elapsed();
            
            // 验证性能
            assert_eq!(handler.get_message_count(), 1000);
            assert!(elapsed < Duration::from_secs(1), "处理1000条消息应该在1秒内完成");
            
            println!("处理1000条消息用时: {:?}", elapsed);
            
            Ok::<(), IpcError>(())
        }).await;

        assert!(result.is_ok(), "性能基准测试失败: {:?}", result.err());
    }
} 