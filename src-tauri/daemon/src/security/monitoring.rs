/// 安全监控系统
/// 
/// 负责守护进程的实时安全监控和威胁检测，包括：
/// - 异常行为检测和分析
/// - 入侵检测和防护
/// - 实时威胁监控
/// - 自动化事件响应
/// - 性能和资源监控

use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;
use tokio::time::interval;
use chrono::{DateTime, Utc};
use tracing as log;
use crate::security::SecurityPolicy;

/// 安全监控错误类型
#[derive(Debug, thiserror::Error)]
pub enum SecurityMonitoringError {
    #[error("异常检测失败: {0}")]
    AnomalyDetectionFailed(String),
    
    #[error("入侵检测失败: {0}")]
    IntrusionDetectionFailed(String),
    
    #[error("威胁监控失败: {0}")]
    ThreatMonitoringFailed(String),
    
    #[error("事件响应失败: {0}")]
    EventResponseFailed(String),
    
    #[error("性能监控失败: {0}")]
    PerformanceMonitoringFailed(String),
    
    #[error("监控配置错误: {0}")]
    ConfigurationError(String),
}

/// 安全监控系统
pub struct SecurityMonitor {
    /// 安全策略
    policy: SecurityPolicy,
    /// 监控配置
    config: MonitoringConfig,
    /// 异常检测器
    anomaly_detector: Arc<AnomalyDetector>,
    /// 入侵检测器
    intrusion_detector: Arc<IntrusionDetector>,
    /// 威胁监控器
    threat_monitor: Arc<ThreatMonitor>,
    /// 事件响应器
    event_responder: Arc<EventResponder>,
    /// 性能监控器
    performance_monitor: Arc<PerformanceMonitor>,
    /// 运行状态
    status: Arc<RwLock<SecurityMonitoringStatus>>,
    /// 监控指标
    metrics: Arc<RwLock<MonitoringMetrics>>,
}

/// 监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    /// 启用异常检测
    pub enable_anomaly_detection: bool,
    /// 启用入侵检测
    pub enable_intrusion_detection: bool,
    /// 启用威胁监控
    pub enable_threat_monitoring: bool,
    /// 启用自动响应
    pub enable_auto_response: bool,
    /// 监控间隔（秒）
    pub monitoring_interval_seconds: u64,
    /// 异常检测阈值
    pub anomaly_threshold: f64,
    /// 威胁检测阈值
    pub threat_threshold: f64,
    /// 最大告警频率（每分钟）
    pub max_alert_frequency: u32,
    /// 数据保留天数
    pub data_retention_days: u32,
}

impl Default for MonitoringConfig {
    fn default() -> Self {
        Self {
            enable_anomaly_detection: true,
            enable_intrusion_detection: true,
            enable_threat_monitoring: true,
            enable_auto_response: true,
            monitoring_interval_seconds: 30,
            anomaly_threshold: 0.8,
            threat_threshold: 0.7,
            max_alert_frequency: 10,
            data_retention_days: 30,
        }
    }
}

/// 安全监控状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityMonitoringStatus {
    pub initialized: bool,
    pub anomaly_detection_enabled: bool,
    pub intrusion_detection_enabled: bool,
    pub threat_monitoring_enabled: bool,
    pub auto_response_enabled: bool,
    pub active_threats: u32,
    pub anomalies_detected: u64,
    pub intrusions_blocked: u64,
    pub alerts_generated: u64,
    pub last_scan_time: Option<DateTime<Utc>>,
}

impl Default for SecurityMonitoringStatus {
    fn default() -> Self {
        Self {
            initialized: false,
            anomaly_detection_enabled: false,
            intrusion_detection_enabled: false,
            threat_monitoring_enabled: false,
            auto_response_enabled: false,
            active_threats: 0,
            anomalies_detected: 0,
            intrusions_blocked: 0,
            alerts_generated: 0,
            last_scan_time: None,
        }
    }
}

/// 监控指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringMetrics {
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub network_activity: NetworkActivity,
    pub disk_activity: DiskActivity,
    pub process_count: u32,
    pub connection_count: u32,
    pub threat_score: f64,
    pub last_updated: DateTime<Utc>,
}

/// 网络活动指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkActivity {
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub packets_sent: u64,
    pub packets_received: u64,
    pub connections_established: u32,
    pub connections_closed: u32,
}

/// 磁盘活动指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiskActivity {
    pub bytes_read: u64,
    pub bytes_written: u64,
    pub read_operations: u64,
    pub write_operations: u64,
}

impl Default for MonitoringMetrics {
    fn default() -> Self {
        Self {
            cpu_usage: 0.0,
            memory_usage: 0.0,
            network_activity: NetworkActivity {
                bytes_sent: 0,
                bytes_received: 0,
                packets_sent: 0,
                packets_received: 0,
                connections_established: 0,
                connections_closed: 0,
            },
            disk_activity: DiskActivity {
                bytes_read: 0,
                bytes_written: 0,
                read_operations: 0,
                write_operations: 0,
            },
            process_count: 0,
            connection_count: 0,
            threat_score: 0.0,
            last_updated: Utc::now(),
        }
    }
}

impl SecurityMonitor {
    /// 创建新的安全监控系统
    pub async fn new(policy: &SecurityPolicy) -> Result<Self, SecurityMonitoringError> {
        let config = MonitoringConfig::default();
        
        let anomaly_detector = Arc::new(
            AnomalyDetector::new(&config).await
                .map_err(|e| SecurityMonitoringError::AnomalyDetectionFailed(e.to_string()))?
        );
        
        let intrusion_detector = Arc::new(
            IntrusionDetector::new(&config).await
                .map_err(|e| SecurityMonitoringError::IntrusionDetectionFailed(e.to_string()))?
        );
        
        let threat_monitor = Arc::new(
            ThreatMonitor::new(&config).await
                .map_err(|e| SecurityMonitoringError::ThreatMonitoringFailed(e.to_string()))?
        );
        
        let event_responder = Arc::new(
            EventResponder::new(&config).await
                .map_err(|e| SecurityMonitoringError::EventResponseFailed(e.to_string()))?
        );
        
        let performance_monitor = Arc::new(
            PerformanceMonitor::new(&config).await
                .map_err(|e| SecurityMonitoringError::PerformanceMonitoringFailed(e.to_string()))?
        );
        
        Ok(Self {
            policy: policy.clone(),
            config,
            anomaly_detector,
            intrusion_detector,
            threat_monitor,
            event_responder,
            performance_monitor,
            status: Arc::new(RwLock::new(SecurityMonitoringStatus::default())),
            metrics: Arc::new(RwLock::new(MonitoringMetrics::default())),
        })
    }
    
    /// 初始化安全监控系统
    pub async fn initialize(&self) -> Result<(), SecurityMonitoringError> {
        log::info!("初始化安全监控系统...");
        
        // 1. 初始化异常检测器
        if self.config.enable_anomaly_detection {
            self.anomaly_detector.initialize().await
                .map_err(|e| SecurityMonitoringError::AnomalyDetectionFailed(e.to_string()))?;
        }
        
        // 2. 初始化入侵检测器
        if self.config.enable_intrusion_detection {
            self.intrusion_detector.initialize().await
                .map_err(|e| SecurityMonitoringError::IntrusionDetectionFailed(e.to_string()))?;
        }
        
        // 3. 初始化威胁监控器
        if self.config.enable_threat_monitoring {
            self.threat_monitor.initialize().await
                .map_err(|e| SecurityMonitoringError::ThreatMonitoringFailed(e.to_string()))?;
        }
        
        // 4. 初始化事件响应器
        if self.config.enable_auto_response {
            self.event_responder.initialize().await
                .map_err(|e| SecurityMonitoringError::EventResponseFailed(e.to_string()))?;
        }
        
        // 5. 初始化性能监控器
        self.performance_monitor.initialize().await
            .map_err(|e| SecurityMonitoringError::PerformanceMonitoringFailed(e.to_string()))?;
        
        // 6. 启动监控循环
        self.start_monitoring_loop().await?;
        
        // 更新状态
        {
            let mut status = self.status.write().await;
            status.initialized = true;
            status.anomaly_detection_enabled = self.config.enable_anomaly_detection;
            status.intrusion_detection_enabled = self.config.enable_intrusion_detection;
            status.threat_monitoring_enabled = self.config.enable_threat_monitoring;
            status.auto_response_enabled = self.config.enable_auto_response;
        }
        
        log::info!("安全监控系统初始化完成");
        Ok(())
    }
    
    /// 启动威胁检测
    pub async fn start_threat_detection(&self) -> Result<(), SecurityMonitoringError> {
        log::info!("启动威胁检测");
        
        // 启动威胁监控
        self.threat_monitor.start_monitoring().await
            .map_err(|e| SecurityMonitoringError::ThreatMonitoringFailed(e.to_string()))?;
        
        // 启动异常检测
        if self.config.enable_anomaly_detection {
            self.anomaly_detector.start_detection().await
                .map_err(|e| SecurityMonitoringError::AnomalyDetectionFailed(e.to_string()))?;
        }
        
        // 启动入侵检测
        if self.config.enable_intrusion_detection {
            self.intrusion_detector.start_detection().await
                .map_err(|e| SecurityMonitoringError::IntrusionDetectionFailed(e.to_string()))?;
        }
        
        Ok(())
    }
    
    /// 启动监控循环
    async fn start_monitoring_loop(&self) -> Result<(), SecurityMonitoringError> {
        let interval_duration = Duration::from_secs(self.config.monitoring_interval_seconds);
        let mut interval_timer = interval(interval_duration);
        
        let status = Arc::clone(&self.status);
        let metrics = Arc::clone(&self.metrics);
        let performance_monitor = Arc::clone(&self.performance_monitor);
        let anomaly_detector = Arc::clone(&self.anomaly_detector);
        let intrusion_detector = Arc::clone(&self.intrusion_detector);
        let threat_monitor = Arc::clone(&self.threat_monitor);
        let event_responder = Arc::clone(&self.event_responder);
        let config = self.config.clone();
        
        tokio::spawn(async move {
            loop {
                interval_timer.tick().await;
                
                // 检查是否仍在运行
                if !status.read().await.initialized {
                    break;
                }
                
                // 更新性能指标
                if let Ok(current_metrics) = performance_monitor.collect_metrics().await {
                    *metrics.write().await = current_metrics;
                }
                
                // 执行异常检测
                if config.enable_anomaly_detection {
                    if let Err(e) = anomaly_detector.detect_anomalies().await {
                        log::error!("异常检测失败: {}", e);
                    }
                }
                
                // 执行入侵检测
                if config.enable_intrusion_detection {
                    if let Err(e) = intrusion_detector.detect_intrusions().await {
                        log::error!("入侵检测失败: {}", e);
                    }
                }
                
                // 执行威胁监控
                if config.enable_threat_monitoring {
                    if let Err(e) = threat_monitor.monitor_threats().await {
                        log::error!("威胁监控失败: {}", e);
                    }
                }
                
                // 更新最后扫描时间
                {
                    let mut status_guard = status.write().await;
                    status_guard.last_scan_time = Some(Utc::now());
                }
            }
        });
        
        Ok(())
    }
    
    /// 获取监控指标
    pub async fn get_metrics(&self) -> MonitoringMetrics {
        self.metrics.read().await.clone()
    }
    
    /// 获取安全监控状态
    pub async fn get_status(&self) -> Result<SecurityMonitoringStatus, SecurityMonitoringError> {
        Ok(self.status.read().await.clone())
    }
    
    /// 关闭安全监控系统
    pub async fn shutdown(&self) -> Result<(), SecurityMonitoringError> {
        log::info!("关闭安全监控系统...");
        
        // 按相反顺序关闭各个组件
        self.performance_monitor.shutdown().await.ok();
        self.event_responder.shutdown().await.ok();
        self.threat_monitor.shutdown().await.ok();
        self.intrusion_detector.shutdown().await.ok();
        self.anomaly_detector.shutdown().await.ok();
        
        // 更新状态
        {
            let mut status = self.status.write().await;
            status.initialized = false;
            status.anomaly_detection_enabled = false;
            status.intrusion_detection_enabled = false;
            status.threat_monitoring_enabled = false;
            status.auto_response_enabled = false;
            status.active_threats = 0;
        }
        
        log::info!("安全监控系统已关闭");
        Ok(())
    }
}

/// 异常检测器
pub struct AnomalyDetector {
    config: MonitoringConfig,
    baseline_metrics: Arc<RwLock<Option<MonitoringMetrics>>>,
    anomaly_history: Arc<RwLock<Vec<AnomalyEvent>>>,
}

/// 异常事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnomalyEvent {
    pub timestamp: DateTime<Utc>,
    pub anomaly_type: AnomalyType,
    pub severity: AnomalySeverity,
    pub description: String,
    pub metric_value: f64,
    pub baseline_value: f64,
    pub deviation: f64,
}

/// 异常类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AnomalyType {
    CpuUsage,
    MemoryUsage,
    NetworkActivity,
    DiskActivity,
    ProcessCount,
    ConnectionCount,
}

/// 异常严重程度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AnomalySeverity {
    Low,
    Medium,
    High,
    Critical,
}

impl AnomalyDetector {
    pub async fn new(config: &MonitoringConfig) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        Ok(Self {
            config: config.clone(),
            baseline_metrics: Arc::new(RwLock::new(None)),
            anomaly_history: Arc::new(RwLock::new(Vec::new())),
        })
    }
    
    pub async fn initialize(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("初始化异常检测器");
        Ok(())
    }
    
    pub async fn start_detection(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("启动异常检测");
        Ok(())
    }
    
    pub async fn detect_anomalies(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 实现异常检测逻辑
        log::debug!("执行异常检测");
        Ok(())
    }
    
    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("关闭异常检测器");
        Ok(())
    }
}

/// 入侵检测器
pub struct IntrusionDetector {
    config: MonitoringConfig,
    detection_rules: Vec<IntrusionRule>,
    intrusion_history: Arc<RwLock<Vec<IntrusionEvent>>>,
}

/// 入侵规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntrusionRule {
    pub id: String,
    pub name: String,
    pub pattern: String,
    pub severity: IntrusionSeverity,
    pub action: IntrusionAction,
}

/// 入侵事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntrusionEvent {
    pub timestamp: DateTime<Utc>,
    pub rule_id: String,
    pub severity: IntrusionSeverity,
    pub description: String,
    pub source: String,
    pub blocked: bool,
}

/// 入侵严重程度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IntrusionSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// 入侵响应动作
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IntrusionAction {
    Log,
    Alert,
    Block,
    Quarantine,
}

impl IntrusionDetector {
    pub async fn new(config: &MonitoringConfig) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        let detection_rules = vec![
            IntrusionRule {
                id: "BRUTE_FORCE_001".to_string(),
                name: "暴力破解攻击".to_string(),
                pattern: "multiple_auth_failures".to_string(),
                severity: IntrusionSeverity::High,
                action: IntrusionAction::Block,
            },
            IntrusionRule {
                id: "PRIVILEGE_ESC_001".to_string(),
                name: "权限提升攻击".to_string(),
                pattern: "privilege_escalation".to_string(),
                severity: IntrusionSeverity::Critical,
                action: IntrusionAction::Quarantine,
            },
        ];
        
        Ok(Self {
            config: config.clone(),
            detection_rules,
            intrusion_history: Arc::new(RwLock::new(Vec::new())),
        })
    }
    
    pub async fn initialize(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("初始化入侵检测器");
        log::info!("加载了 {} 个入侵检测规则", self.detection_rules.len());
        Ok(())
    }
    
    pub async fn start_detection(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("启动入侵检测");
        Ok(())
    }
    
    pub async fn detect_intrusions(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 实现入侵检测逻辑
        log::debug!("执行入侵检测");
        Ok(())
    }
    
    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("关闭入侵检测器");
        Ok(())
    }
}

/// 威胁监控器
pub struct ThreatMonitor {
    config: MonitoringConfig,
    threat_signatures: Vec<ThreatSignature>,
    active_threats: Arc<RwLock<HashMap<String, ThreatEvent>>>,
}

/// 威胁签名
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatSignature {
    pub id: String,
    pub name: String,
    pub pattern: String,
    pub severity: ThreatSeverity,
    pub category: ThreatCategory,
}

/// 威胁事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatEvent {
    pub timestamp: DateTime<Utc>,
    pub threat_id: String,
    pub severity: ThreatSeverity,
    pub category: ThreatCategory,
    pub description: String,
    pub source: String,
    pub mitigated: bool,
}

/// 威胁严重程度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ThreatSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// 威胁类别
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ThreatCategory {
    Malware,
    Phishing,
    DataExfiltration,
    DenialOfService,
    PrivilegeEscalation,
    Reconnaissance,
}

impl ThreatMonitor {
    pub async fn new(config: &MonitoringConfig) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        let threat_signatures = vec![
            ThreatSignature {
                id: "MALWARE_001".to_string(),
                name: "恶意软件检测".to_string(),
                pattern: "malware_signature".to_string(),
                severity: ThreatSeverity::Critical,
                category: ThreatCategory::Malware,
            },
            ThreatSignature {
                id: "PHISHING_001".to_string(),
                name: "钓鱼攻击检测".to_string(),
                pattern: "phishing_pattern".to_string(),
                severity: ThreatSeverity::High,
                category: ThreatCategory::Phishing,
            },
        ];
        
        Ok(Self {
            config: config.clone(),
            threat_signatures,
            active_threats: Arc::new(RwLock::new(HashMap::new())),
        })
    }
    
    pub async fn initialize(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("初始化威胁监控器");
        log::info!("加载了 {} 个威胁签名", self.threat_signatures.len());
        Ok(())
    }
    
    pub async fn start_monitoring(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("启动威胁监控");
        Ok(())
    }
    
    pub async fn monitor_threats(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 实现威胁监控逻辑
        log::debug!("执行威胁监控");
        Ok(())
    }
    
    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("关闭威胁监控器");
        Ok(())
    }
}

/// 事件响应器
pub struct EventResponder {
    config: MonitoringConfig,
    response_rules: Vec<ResponseRule>,
    response_history: Arc<RwLock<Vec<ResponseEvent>>>,
}

/// 响应规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseRule {
    pub id: String,
    pub name: String,
    pub trigger_condition: String,
    pub response_action: ResponseAction,
    pub severity_threshold: ResponseSeverity,
}

/// 响应事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseEvent {
    pub timestamp: DateTime<Utc>,
    pub rule_id: String,
    pub action: ResponseAction,
    pub success: bool,
    pub description: String,
}

/// 响应动作
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResponseAction {
    Log,
    Alert,
    Block,
    Isolate,
    Terminate,
    Quarantine,
}

/// 响应严重程度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResponseSeverity {
    Low,
    Medium,
    High,
    Critical,
}

impl EventResponder {
    pub async fn new(config: &MonitoringConfig) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        let response_rules = vec![
            ResponseRule {
                id: "AUTO_BLOCK_001".to_string(),
                name: "自动阻断高风险连接".to_string(),
                trigger_condition: "high_risk_connection".to_string(),
                response_action: ResponseAction::Block,
                severity_threshold: ResponseSeverity::High,
            },
            ResponseRule {
                id: "AUTO_ISOLATE_001".to_string(),
                name: "自动隔离恶意进程".to_string(),
                trigger_condition: "malicious_process".to_string(),
                response_action: ResponseAction::Isolate,
                severity_threshold: ResponseSeverity::Critical,
            },
        ];
        
        Ok(Self {
            config: config.clone(),
            response_rules,
            response_history: Arc::new(RwLock::new(Vec::new())),
        })
    }
    
    pub async fn initialize(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("初始化事件响应器");
        log::info!("加载了 {} 个响应规则", self.response_rules.len());
        Ok(())
    }
    
    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("关闭事件响应器");
        Ok(())
    }
}

/// 性能监控器
pub struct PerformanceMonitor {
    config: MonitoringConfig,
}

impl PerformanceMonitor {
    pub async fn new(config: &MonitoringConfig) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        Ok(Self {
            config: config.clone(),
        })
    }
    
    pub async fn initialize(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("初始化性能监控器");
        Ok(())
    }
    
    pub async fn collect_metrics(&self) -> Result<MonitoringMetrics, Box<dyn std::error::Error + Send + Sync>> {
        // 实现性能指标收集逻辑
        // 这里应该收集实际的系统指标
        Ok(MonitoringMetrics {
            cpu_usage: 25.0,
            memory_usage: 512.0,
            network_activity: NetworkActivity {
                bytes_sent: 1024,
                bytes_received: 2048,
                packets_sent: 10,
                packets_received: 20,
                connections_established: 5,
                connections_closed: 3,
            },
            disk_activity: DiskActivity {
                bytes_read: 4096,
                bytes_written: 2048,
                read_operations: 10,
                write_operations: 5,
            },
            process_count: 50,
            connection_count: 10,
            threat_score: 0.1,
            last_updated: Utc::now(),
        })
    }
    
    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("关闭性能监控器");
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::security::SecurityPolicy;
    
    #[tokio::test]
    async fn test_security_monitor_creation() {
        let policy = SecurityPolicy::default();
        let monitor = SecurityMonitor::new(&policy).await;
        assert!(monitor.is_ok());
    }
    
    #[tokio::test]
    async fn test_anomaly_detector_creation() {
        let config = MonitoringConfig::default();
        let detector = AnomalyDetector::new(&config).await;
        assert!(detector.is_ok());
    }
    
    #[tokio::test]
    async fn test_intrusion_detector_creation() {
        let config = MonitoringConfig::default();
        let detector = IntrusionDetector::new(&config).await;
        assert!(detector.is_ok());
    }
    
    #[tokio::test]
    async fn test_threat_monitor_creation() {
        let config = MonitoringConfig::default();
        let monitor = ThreatMonitor::new(&config).await;
        assert!(monitor.is_ok());
    }
    
    #[tokio::test]
    async fn test_event_responder_creation() {
        let config = MonitoringConfig::default();
        let responder = EventResponder::new(&config).await;
        assert!(responder.is_ok());
    }
    
    #[tokio::test]
    async fn test_performance_monitor_creation() {
        let config = MonitoringConfig::default();
        let monitor = PerformanceMonitor::new(&config).await;
        assert!(monitor.is_ok());
    }
    
    #[tokio::test]
    async fn test_metrics_collection() {
        let config = MonitoringConfig::default();
        let monitor = PerformanceMonitor::new(&config).await.unwrap();
        
        let metrics = monitor.collect_metrics().await;
        assert!(metrics.is_ok());
        
        let metrics = metrics.unwrap();
        assert!(metrics.cpu_usage >= 0.0);
        assert!(metrics.memory_usage >= 0.0);
        assert!(metrics.threat_score >= 0.0);
    }
    
    #[tokio::test]
    async fn test_monitoring_status() {
        let policy = SecurityPolicy::default();
        let monitor = SecurityMonitor::new(&policy).await.unwrap();
        
        let status = monitor.get_status().await.unwrap();
        assert!(!status.initialized);
        assert_eq!(status.active_threats, 0);
    }
} 